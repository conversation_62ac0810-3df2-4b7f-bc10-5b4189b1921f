@echo off
echo ========================================
echo      编译测试和验证脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [+] 管理员权限确认
) else (
    echo [-] 需要管理员权限
    echo [*] 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 设置编译环境
echo [*] 设置编译环境...
set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build"
if not exist "%VS_PATH%\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build"
)

if not exist "%VS_PATH%\vcvars64.bat" (
    echo [-] 找不到Visual Studio
    pause
    exit /b 1
)

call "%VS_PATH%\vcvars64.bat" >nul 2>&1

REM 清理旧文件
echo [*] 清理旧文件...
if exist "bin" rmdir /s /q bin
if exist "*.obj" del *.obj
if exist "*.pdb" del *.pdb
if exist "*.ilk" del *.ilk

REM 创建输出目录
mkdir bin

echo [*] 开始编译测试...
echo.

REM 测试编译简化版本
echo [测试1] 编译简化测试版本...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\simple_game_test.exe simple_game_test.cpp drv.cpp ntdll.lib >compile_test1.log 2>&1
if %errorLevel% == 0 (
    echo [+] simple_game_test.exe 编译成功
    set "TEST1_OK=1"
) else (
    echo [-] simple_game_test.exe 编译失败
    echo [*] 错误详情:
    type compile_test1.log
    set "TEST1_OK=0"
)

echo.

REM 测试编译完整版本
echo [测试2] 编译完整游戏读取器...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\game_reader.exe game_reader.cpp drv.cpp ntdll.lib >compile_test2.log 2>&1
if %errorLevel% == 0 (
    echo [+] game_reader.exe 编译成功
    set "TEST2_OK=1"
) else (
    echo [-] game_reader.exe 编译失败
    echo [*] 错误详情:
    type compile_test2.log
    set "TEST2_OK=0"
)

echo.

REM 测试编译原始版本
echo [测试3] 编译原始测试程序...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\wnbios_poc.exe wnbios_poc.cpp drv.cpp ntdll.lib >compile_test3.log 2>&1
if %errorLevel% == 0 (
    echo [+] wnbios_poc.exe 编译成功
    set "TEST3_OK=1"
) else (
    echo [-] wnbios_poc.exe 编译失败
    echo [*] 错误详情:
    type compile_test3.log
    set "TEST3_OK=0"
)

echo.

REM 清理临时文件
echo [*] 清理临时文件...
del *.obj >nul 2>&1
del *.pdb >nul 2>&1
del *.ilk >nul 2>&1

echo ========================================
echo           编译测试结果
echo ========================================
echo.

if "%TEST1_OK%"=="1" (
    echo [+] simple_game_test.exe - 编译成功
) else (
    echo [-] simple_game_test.exe - 编译失败
)

if "%TEST2_OK%"=="1" (
    echo [+] game_reader.exe - 编译成功
) else (
    echo [-] game_reader.exe - 编译失败
)

if "%TEST3_OK%"=="1" (
    echo [+] wnbios_poc.exe - 编译成功
) else (
    echo [-] wnbios_poc.exe - 编译失败
)

echo.

REM 检查文件大小
if exist "bin\simple_game_test.exe" (
    for %%A in (bin\simple_game_test.exe) do echo [信息] simple_game_test.exe 大小: %%~zA 字节
)
if exist "bin\game_reader.exe" (
    for %%A in (bin\game_reader.exe) do echo [信息] game_reader.exe 大小: %%~zA 字节
)
if exist "bin\wnbios_poc.exe" (
    for %%A in (bin\wnbios_poc.exe) do echo [信息] wnbios_poc.exe 大小: %%~zA 字节
)

echo.

REM 总结
if "%TEST1_OK%"=="1" if "%TEST2_OK%"=="1" if "%TEST3_OK%"=="1" (
    echo [成功] 所有程序编译成功！
    echo.
    echo 使用说明:
    echo   1. 以管理员身份运行 bin\simple_game_test.exe 进行基本测试
    echo   2. 以管理员身份运行 bin\game_reader.exe 进行完整功能测试
    echo   3. 详细说明请查看 README_GAME_READER.md
) else (
    echo [失败] 部分程序编译失败
    echo [建议] 请检查编译日志文件 compile_test*.log
    echo [建议] 确保Visual Studio安装完整
)

echo.
echo [*] 按任意键退出...
pause >nul

REM 清理日志文件
del compile_test*.log >nul 2>&1
