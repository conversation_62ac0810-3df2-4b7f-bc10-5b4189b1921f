﻿  drv.cpp
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(74,1): error C2059: 语法错误:“}”
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(74,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(77,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(77,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1261,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,3): error C2059: 语法错误:“if”
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,36): error C2065: “ptr”: 未声明的标识符
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,19): error C7568: 假定的函数模板“ptr”后面缺少参数列表
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,58): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1461,3): error C2059: 语法错误:“return”
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1461,48): error C2238: 意外的标记位于“;”之前
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,29): error C2146: 语法错误: 缺少“;”(在标识符“read_player_info”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,56): error C2146: 语法错误: 缺少“)”(在标识符“player_address”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46): error C2377: “uintptr_t”: 重定义；typedef 不能由任何其他符号重载
  (编译源文件“drv.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vadefs.h(61,35):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,56): error C2146: 语法错误: 缺少“;”(在标识符“player_address”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,70): error C2059: 语法错误:“)”
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1348,3): error C2065: “monitoring_active”: 未声明的标识符
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1354,7): error C2065: “monitoring_active”: 未声明的标识符
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1355,4): error C3861: “stop_player_monitoring”: 找不到标识符
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1452,10): error C2672: “eneio_lib::safe_read_memory”: 未找到匹配的重载函数
  (编译源文件“drv.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1442,4):
      可能是“T eneio_lib::safe_read_memory(uintptr_t)”
          C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1452,27):
          “eneio_lib::safe_read_memory”:“T”的 模板 参数无效，应为类型
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1466,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1466,2): error C2086: “int uintptr_t”: 重定义
  (编译源文件“drv.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1466,12): error C2146: 语法错误: 缺少“;”(在标识符“get_local_player”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1467,7): error C2923: "std::vector": "GameStructures::PlayerInfo" 不是参数 "_Ty" 的有效 模板 类型参数
  (编译源文件“drv.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,18):
      参见“GameStructures::PlayerInfo”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1467,7): error C2976: “std::vector'”: 模板 参数太少
  (编译源文件“drv.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1467,42): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
  (编译源文件“drv.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1468,37): error C2146: 语法错误: 缺少“)”(在标识符“actor_ptr”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1473,7): error C2182: “debug_memory_info”: “void” 这一使用无效
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1473,35): error C2146: 语法错误: 缺少“)”(在标识符“address”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1479,1): error C2059: 语法错误:“private”
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1482,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1482,2): error C2086: “int uintptr_t”: 重定义
  (编译源文件“drv.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1482,12): error C2146: 语法错误: 缺少“;”(在标识符“game_base_address”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1485,1): error C2059: 语法错误:“}”
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1485,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“drv.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(14,17): error C2653: “driver”: 不是类或命名空间名称
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(14,13): error C2530: “c”: 必须初始化引用
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(14,13): error C3531: “c”: 类型包含“auto”的符号必须具有初始值设定项
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(14,15): error C2143: 语法错误: 缺少“;”(在“:”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(14,25): error C2065: “eneio64”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(14,32): error C2143: 语法错误: 缺少“;”(在“)”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(294,22): error C2143: 语法错误: 缺少“;”(在“eneio_lib::leak_kprocess”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(294,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(294,1): error C2086: “int uintptr_t”: 重定义
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(294,22): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(294,22): error C2556: “int eneio_lib::leak_kprocess(void)”: 重载函数与“uintptr_t eneio_lib::leak_kprocess(void)”只是在返回类型上不同
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1394,12):
      参见“eneio_lib::leak_kprocess”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(294,22): error C2371: “eneio_lib::leak_kprocess”: 重定义；不同的基类型
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1394,12):
      参见“eneio_lib::leak_kprocess”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(296,7): error C2923: "std::vector": "uintptr_t" 不是参数 "_Ty" 的有效 模板 类型参数
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(296,7): error C2976: “std::vector'”: 模板 参数太少
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(296,25): error C2641: 无法推导“std::vector”的模板参数
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(296,25): error C2783: “std::vector<_Ty,_Alloc> std::vector(void) noexcept(<expr>)”: 无法推导“_Ty”的 模板 参数
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(670,5):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(296,25): error C2780: “std::vector<_Ty,_Alloc> std::vector(std::vector<_Ty,_Alloc>)”: 应输入 1 个参数，却提供了 0 个
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(298,7): error C2664: “bool eneio_lib::leak_kpointers(std::vector<uintptr_t,std::allocator<uintptr_t>> &)”: 无法将参数 1 从“std::vector”转换为“std::vector<uintptr_t,std::allocator<uintptr_t>> &”
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1395,7):
      参见“eneio_lib::leak_kpointers”的声明
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(298,7):
      尝试匹配参数列表“(std::vector)”时
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(305,17): error C2146: 语法错误: 缺少“;”(在标识符“pointer”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(305,25): error C2143: 语法错误: 缺少“;”(在“:”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(305,25): error C2143: 语法错误: 缺少“)”(在“:”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(305,25): error C2059: 语法错误:“:”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(305,35): error C2059: 语法错误:“)”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(306,2): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(309,23): error C2065: “pointer”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(313,11): error C2065: “pointer”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(314,4): error C2043: 非法 break
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(322,37): error C2923: "std::vector": "uintptr_t" 不是参数 "_Ty" 的有效 模板 类型参数
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(322,37): error C2976: “std::vector'”: 模板 参数太少
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(322,56): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(322,17): error C2511: “bool eneio_lib::leak_kpointers(std::vector &)”:“eneio_lib”中没有找到重载的成员函数
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1284,7):
      参见“eneio_lib”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(349,40): error C2061: 语法错误: 标识符“uintptr_t”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(358,22): error C2143: 语法错误: 缺少“;”(在“eneio_lib::map_physical”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(358,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(358,1): error C2086: “int uintptr_t”: 重定义
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(358,22): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(358,22): error C2556: “int eneio_lib::map_physical(uint64_t,size_t,eneio_mem &)”: 重载函数与“uintptr_t eneio_lib::map_physical(uint64_t,size_t,eneio_mem &)”只是在返回类型上不同
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1398,12):
      参见“eneio_lib::map_physical”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(358,22): error C2371: “eneio_lib::map_physical”: 重定义；不同的基类型
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1398,12):
      参见“eneio_lib::map_physical”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(365,2): warning C4390: “;”: 找到空的受控语句；这是否是有意的?
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(371,22): error C2143: 语法错误: 缺少“;”(在“eneio_lib::unmap_physical”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(371,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(371,1): error C2086: “int uintptr_t”: 重定义
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(371,22): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(371,22): error C2556: “int eneio_lib::unmap_physical(eneio_mem &)”: 重载函数与“uintptr_t eneio_lib::unmap_physical(eneio_mem &)”只是在返回类型上不同
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1399,12):
      参见“eneio_lib::unmap_physical”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(371,22): error C2371: “eneio_lib::unmap_physical”: 重定义；不同的基类型
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1399,12):
      参见“eneio_lib::unmap_physical”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(375,2): warning C4390: “;”: 找到空的受控语句；这是否是有意的?
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(381,22): error C2143: 语法错误: 缺少“;”(在“eneio_lib::get_system_dirbase”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(381,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(381,1): error C2086: “int uintptr_t”: 重定义
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(381,22): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(381,22): error C2556: “int eneio_lib::get_system_dirbase(void)”: 重载函数与“uintptr_t eneio_lib::get_system_dirbase(void)”只是在返回类型上不同
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1393,12):
      参见“eneio_lib::get_system_dirbase”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(381,22): error C2371: “eneio_lib::get_system_dirbase”: 重定义；不同的基类型
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1393,12):
      参见“eneio_lib::get_system_dirbase”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(386,13): error C2146: 语法错误: 缺少“;”(在标识符“lpBuffer”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(386,13): error C2065: “lpBuffer”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(386,36): error C3867: “eneio_lib::map_physical”: 非标准语法；请使用 "&" 来创建指向成员的指针
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(390,69): error C2061: 语法错误: 标识符“uintptr_t”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(392,69): error C2061: 语法错误: 标识符“uintptr_t”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(394,47): error C2061: 语法错误: 标识符“uintptr_t”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(397,29): error C2061: 语法错误: 标识符“uintptr_t”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(406,22): error C2143: 语法错误: 缺少“;”(在“eneio_lib::get_process_id”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(406,1): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(406,1): error C2086: “int uintptr_t”: 重定义
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.cpp(406,1): error C1003: 错误计数超过 100；正在停止编译
  wnbios_poc.cpp
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(74,1): error C2059: 语法错误:“}”
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(74,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(77,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(77,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1261,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,3): error C2059: 语法错误:“if”
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,36): error C2065: “ptr”: 未声明的标识符
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,19): error C7568: 假定的函数模板“ptr”后面缺少参数列表
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1456,58): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1461,3): error C2059: 语法错误:“return”
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1461,48): error C2238: 意外的标记位于“;”之前
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,29): error C2146: 语法错误: 缺少“;”(在标识符“read_player_info”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,56): error C2146: 语法错误: 缺少“)”(在标识符“player_address”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46): error C2377: “uintptr_t”: 重定义；typedef 不能由任何其他符号重载
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vadefs.h(61,35):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,56): error C2146: 语法错误: 缺少“;”(在标识符“player_address”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,70): error C2059: 语法错误:“)”
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1348,3): error C2065: “monitoring_active”: 未声明的标识符
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1354,7): error C2065: “monitoring_active”: 未声明的标识符
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1355,4): error C3861: “stop_player_monitoring”: 找不到标识符
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1452,10): error C2672: “eneio_lib::safe_read_memory”: 未找到匹配的重载函数
  (编译源文件“wnbios_poc.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1442,4):
      可能是“T eneio_lib::safe_read_memory(uintptr_t)”
          C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1452,27):
          “eneio_lib::safe_read_memory”:“T”的 模板 参数无效，应为类型
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1466,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1466,2): error C2086: “int uintptr_t”: 重定义
  (编译源文件“wnbios_poc.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1466,12): error C2146: 语法错误: 缺少“;”(在标识符“get_local_player”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1467,7): error C2923: "std::vector": "GameStructures::PlayerInfo" 不是参数 "_Ty" 的有效 模板 类型参数
  (编译源文件“wnbios_poc.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,18):
      参见“GameStructures::PlayerInfo”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1467,7): error C2976: “std::vector'”: 模板 参数太少
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1467,42): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
  (编译源文件“wnbios_poc.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1468,37): error C2146: 语法错误: 缺少“)”(在标识符“actor_ptr”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1473,7): error C2182: “debug_memory_info”: “void” 这一使用无效
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1473,35): error C2146: 语法错误: 缺少“)”(在标识符“address”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1479,1): error C2059: 语法错误:“private”
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1482,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1482,2): error C2086: “int uintptr_t”: 重定义
  (编译源文件“wnbios_poc.cpp”)
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1465,46):
      参见“uintptr_t”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1482,12): error C2146: 语法错误: 缺少“;”(在标识符“game_base_address”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1485,1): error C2059: 语法错误:“}”
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\drv.h(1485,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  (编译源文件“wnbios_poc.cpp”)
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(27,7): error C2065: “len”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(27,25): error C2065: “len”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(27,32): error C7732: “]”前面应该有一个表达式
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(28,11): error C2065: “len”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(28,18): error C7732: “]”前面应该有一个表达式
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(36,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(36,2): error C2365: “time”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h(548,42):
      参见“time”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(37,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(37,2): error C2040: “timeinfo”:“int”与“tm *”的间接寻址级别不同
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(38,2): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(38,2): error C2365: “strftime”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h(268,25):
      参见“strftime”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(38,11): error C2065: “buffer”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(38,19): error C2065: “buffer_size”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(39,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(39,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(42,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(42,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(216,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(216,3): error C2365: “printf”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h(950,37):
      参见“printf”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(216,58): error C2065: “current_scan”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(219,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(219,3): error C2365: “printf”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h(950,37):
      参见“printf”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(219,47): error C2065: “process_name”: 未声明的标识符
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(222,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(222,3): error C2365: “safe_input”: 重定义；以前的定义是“函数”
      C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(23,6):
      参见“safe_input”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(225,3): error C2059: 语法错误:“if”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(225,39): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(225,39): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(272,3): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(272,3): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(275,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(275,3): error C2365: “printf”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h(950,37):
      参见“printf”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(276,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(276,3): error C2365: “system”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdlib.h(1211,26):
      参见“system”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(278,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(278,3): error C2365: “printf”: 重定义；以前的定义是“函数”
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h(950,37):
      参见“printf”的声明
  
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(279,3): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(279,3): error C2040: “g_driver”:“int”与“eneio_lib *”的间接寻址级别不同
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(281,2): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(281,2): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(282,34): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(282,34): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(288,2): error C2059: 语法错误:“catch”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(288,14): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(288,14): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(294,1): error C2059: 语法错误:“}”
C:\Users\<USER>\Desktop\sjz\wnbios_poc\wnbios_poc.cpp(294,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
  正在生成代码...
