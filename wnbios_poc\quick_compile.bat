@echo off
echo ========================================
echo      快速编译测试脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [+] 管理员权限确认
) else (
    echo [-] 需要管理员权限运行此脚本
    pause
    exit /b 1
)

REM 设置编译环境
echo [*] 设置编译环境...
set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
set "VS2019_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

if exist "%VS2022_PATH%" (
    echo [*] 找到 Visual Studio 2022
    call "%VS2022_PATH%" >nul 2>&1
) else if exist "%VS2019_PATH%" (
    echo [*] 找到 Visual Studio 2019
    call "%VS2019_PATH%" >nul 2>&1
) else (
    echo [-] 找不到Visual Studio编译环境
    echo [*] 请确保已安装 Visual Studio 2019 或 2022
    pause
    exit /b 1
)

REM 清理和创建目录
if exist "bin" rmdir /s /q bin
mkdir bin
del *.obj >nul 2>&1

echo [*] 开始编译...
echo.

REM 编译简化测试版本
echo [1/3] 编译简化测试版本...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\simple_game_test.exe simple_game_test.cpp drv.cpp ntdll.lib
if %errorLevel% == 0 (
    echo [+] simple_game_test.exe 编译成功
) else (
    echo [-] simple_game_test.exe 编译失败
    goto :error
)

echo.

REM 编译完整游戏读取器
echo [2/3] 编译完整游戏读取器...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\game_reader.exe game_reader.cpp drv.cpp ntdll.lib
if %errorLevel% == 0 (
    echo [+] game_reader.exe 编译成功
) else (
    echo [-] game_reader.exe 编译失败
    goto :error
)

echo.

REM 编译原始测试程序
echo [3/3] 编译原始测试程序...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\wnbios_poc.exe wnbios_poc.cpp drv.cpp ntdll.lib
if %errorLevel% == 0 (
    echo [+] wnbios_poc.exe 编译成功
) else (
    echo [-] wnbios_poc.exe 编译失败
    goto :error
)

REM 清理临时文件
del *.obj >nul 2>&1

echo.
echo ========================================
echo           编译成功完成！
echo ========================================
echo.
echo 编译输出:
dir bin\*.exe
echo.
echo 使用说明:
echo   1. 以管理员身份运行 bin\simple_game_test.exe 进行基本测试
echo   2. 以管理员身份运行 bin\game_reader.exe 进行完整功能测试
echo   3. 详细说明请查看 README_GAME_READER.md
echo.
echo [*] 按任意键退出...
pause >nul
exit /b 0

:error
echo.
echo ========================================
echo           编译失败！
echo ========================================
echo.
echo 可能的解决方案:
echo   1. 确保以管理员身份运行
echo   2. 检查Visual Studio是否正确安装
echo   3. 确保所有源文件都在当前目录
echo   4. 查看上面的错误信息
echo.
echo [*] 按任意键退出...
pause >nul
exit /b 1
