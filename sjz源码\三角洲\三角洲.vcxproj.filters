﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Utils">
      <UniqueIdentifier>{c02dc536-6684-4262-89e0-f87ccdbd73f4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Engine">
      <UniqueIdentifier>{49e216f8-55a1-4470-ab54-2aa56e0b5a28}</UniqueIdentifier>
    </Filter>
    <Filter Include="Hooks">
      <UniqueIdentifier>{a714b40b-c3fd-4e71-98d1-7b2a58d1e5e7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Render">
      <UniqueIdentifier>{4e0d337f-07a5-42ed-8554-5e42a51971ef}</UniqueIdentifier>
    </Filter>
    <Filter Include="SpooFcall">
      <UniqueIdentifier>{a63830d4-3cc0-46ea-8aea-15d6c342a6bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="AimBot">
      <UniqueIdentifier>{92e25a9f-9568-4930-a572-d059c64500a3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tools">
      <UniqueIdentifier>{da3cf0cc-6403-4357-8a2a-c53e8b90dbef}</UniqueIdentifier>
    </Filter>
    <Filter Include="Iteam">
      <UniqueIdentifier>{d5357539-59ab-4ea5-81f6-ad34401a5846}</UniqueIdentifier>
    </Filter>
    <Filter Include="VehHook">
      <UniqueIdentifier>{146e545b-cecb-4ec2-95a8-b910ec1ff16f}</UniqueIdentifier>
    </Filter>
    <Filter Include="资源文件\Class">
      <UniqueIdentifier>{fa654a33-8af0-4cdf-9b40-c3a851699385}</UniqueIdentifier>
    </Filter>
    <Filter Include="Actors">
      <UniqueIdentifier>{9fca5f43-7a3d-4b97-8055-219aa6ada769}</UniqueIdentifier>
    </Filter>
    <Filter Include="Menu">
      <UniqueIdentifier>{78ce8adc-d68b-4eb5-8a29-1456f2f8bd2d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="Utils.cpp">
      <Filter>Utils</Filter>
    </ClCompile>
    <ClCompile Include="Class.cpp">
      <Filter>资源文件\Class</Filter>
    </ClCompile>
    <ClCompile Include="Engine.cpp">
      <Filter>Engine</Filter>
    </ClCompile>
    <ClCompile Include="Hooks.cpp">
      <Filter>Hooks</Filter>
    </ClCompile>
    <ClCompile Include="Render.cpp">
      <Filter>Render</Filter>
    </ClCompile>
    <ClCompile Include="Aimbot.cpp">
      <Filter>AimBot</Filter>
    </ClCompile>
    <ClCompile Include="Tools.cpp">
      <Filter>Tools</Filter>
    </ClCompile>
    <ClCompile Include="iteam.cpp">
      <Filter>Iteam</Filter>
    </ClCompile>
    <ClCompile Include="hook.cpp">
      <Filter>VehHook</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="base.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="Utils.h">
      <Filter>Utils</Filter>
    </ClInclude>
    <ClInclude Include="Class.h">
      <Filter>资源文件\Class</Filter>
    </ClInclude>
    <ClInclude Include="Engine.h">
      <Filter>Engine</Filter>
    </ClInclude>
    <ClInclude Include="Hooks.h">
      <Filter>Hooks</Filter>
    </ClInclude>
    <ClInclude Include="Render.h">
      <Filter>Render</Filter>
    </ClInclude>
    <ClInclude Include="Spoofcall.h">
      <Filter>SpooFcall</Filter>
    </ClInclude>
    <ClInclude Include="Aimbot.h">
      <Filter>AimBot</Filter>
    </ClInclude>
    <ClInclude Include="Tools.h">
      <Filter>Tools</Filter>
    </ClInclude>
    <ClInclude Include="iteam.h">
      <Filter>Iteam</Filter>
    </ClInclude>
    <ClInclude Include="hook.h">
      <Filter>VehHook</Filter>
    </ClInclude>
    <ClInclude Include="AimText.h">
      <Filter>AimBot</Filter>
    </ClInclude>
    <ClInclude Include="Menu.h">
      <Filter>Menu</Filter>
    </ClInclude>
    <ClInclude Include="Actors.h">
      <Filter>Actors</Filter>
    </ClInclude>
    <ClInclude Include="xor.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <MASM Include="Spoofcall.masm">
      <Filter>SpooFcall</Filter>
    </MASM>
    <MASM Include="syscall.asm">
      <Filter>头文件</Filter>
    </MASM>
  </ItemGroup>
</Project>