# 编译指南

## 快速编译

### 方法1：使用批处理脚本（推荐）
```bash
# 以管理员身份运行
build_game_reader.bat
```

### 方法2：手动编译
打开 **Developer Command Prompt for VS 2022**（或VS 2019），然后运行：

```bash
# 创建输出目录
mkdir bin

# 编译简化测试版本
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\simple_game_test.exe simple_game_test.cpp drv.cpp ntdll.lib

# 编译完整游戏读取器
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\game_reader.exe game_reader.cpp drv.cpp ntdll.lib

# 编译原始测试程序
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\wnbios_poc.exe wnbios_poc.cpp drv.cpp ntdll.lib
```

## 编译选项说明

- `/EHsc` - 启用C++异常处理
- `/O2` - 优化代码（速度优先）
- `/MT` - 静态链接运行时库
- `/D_CRT_SECURE_NO_WARNINGS` - 禁用CRT安全警告
- `/wd4996` - 禁用特定警告（如localtime函数）
- `/Fe:` - 指定输出文件名
- `ntdll.lib` - 链接ntdll库

## 常见编译错误及解决方案

### 1. "无法找到 cl.exe"
**解决方案：**
- 确保已安装 Visual Studio 2019/2022
- 使用 "Developer Command Prompt" 而不是普通命令提示符
- 或者运行：`"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"`

### 2. "C2084: 函数已有主体"
**解决方案：**
- 这通常是重复定义错误
- 确保没有在头文件中定义函数体
- 检查是否有重复的源文件

### 3. "LNK2019: 无法解析的外部符号"
**解决方案：**
- 确保链接了 `ntdll.lib`
- 检查函数声明和定义是否匹配

### 4. "C4996: 'localtime': This function may be unsafe"
**解决方案：**
- 已通过 `/D_CRT_SECURE_NO_WARNINGS` 和 `/wd4996` 解决
- 或者在代码开头添加 `#pragma warning(disable: 4996)`

## 验证编译结果

编译成功后，应该在 `bin\` 目录下看到：
- `simple_game_test.exe` - 简化测试版本
- `game_reader.exe` - 完整游戏读取器
- `wnbios_poc.exe` - 原始测试程序

## 运行要求

- Windows 10/11 x64
- 管理员权限
- 目标进程正在运行

## 测试编译结果

```bash
# 测试简化版本
bin\simple_game_test.exe

# 测试完整版本
bin\game_reader.exe
```

## 调试编译问题

如果编译失败，请：
1. 检查 `compile_log.txt` 文件中的详细错误信息
2. 确保所有源文件都在同一目录
3. 验证 Visual Studio 安装是否完整
4. 尝试清理临时文件后重新编译

## 清理临时文件

```bash
del *.obj
del *.pdb
del *.ilk
del compile_log.txt
```
