========================================
        手动编译指南
========================================

如果批处理脚本无法正常工作，请按照以下步骤手动编译：

步骤1: 打开 Developer Command Prompt
----------------------------------------
1. 按 Win + R，输入 cmd，按 Ctrl+Shift+Enter 以管理员身份运行
2. 或者在开始菜单搜索 "Developer Command Prompt for VS 2022"
3. 右键选择 "以管理员身份运行"

步骤2: 导航到项目目录
----------------------------------------
cd /d "C:\Users\<USER>\Desktop\sjz\wnbios_poc"

步骤3: 设置编译环境（如果需要）
----------------------------------------
如果不是在 Developer Command Prompt 中，请运行：

对于 VS 2022:
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

对于 VS 2019:
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

步骤4: 创建输出目录
----------------------------------------
mkdir bin

步骤5: 编译程序
----------------------------------------

编译简化测试版本:
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\simple_game_test.exe simple_game_test.cpp drv.cpp ntdll.lib

编译完整游戏读取器:
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\game_reader.exe game_reader.cpp drv.cpp ntdll.lib

编译原始测试程序:
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\wnbios_poc.exe wnbios_poc.cpp drv.cpp ntdll.lib

步骤6: 清理临时文件
----------------------------------------
del *.obj
del *.pdb
del *.ilk

步骤7: 验证编译结果
----------------------------------------
dir bin\*.exe

应该看到以下文件:
- simple_game_test.exe
- game_reader.exe  
- wnbios_poc.exe

步骤8: 测试运行
----------------------------------------
以管理员身份运行:
bin\simple_game_test.exe

========================================
        常见问题解决
========================================

问题1: "'cl' 不是内部或外部命令"
解决: 确保在 Developer Command Prompt 中运行，或先运行 vcvars64.bat

问题2: "无法打开包含文件"
解决: 确保所有源文件 (.cpp, .h) 都在当前目录

问题3: "LNK2019: 无法解析的外部符号"
解决: 确保链接了 ntdll.lib

问题4: 编译成功但运行时出错
解决: 确保以管理员身份运行编译后的程序

问题5: "拒绝访问"
解决: 以管理员身份运行命令提示符

========================================
        编译选项说明
========================================

/EHsc     - 启用C++异常处理
/O2       - 优化代码（速度优先）
/MT       - 静态链接运行时库
/D_CRT_SECURE_NO_WARNINGS - 禁用CRT安全警告
/wd4996   - 禁用特定警告
/Fe:      - 指定输出文件名
ntdll.lib - 链接ntdll库

========================================
