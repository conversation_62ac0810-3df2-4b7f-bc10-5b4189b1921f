# 基于wnbios的游戏数据读取器

## 项目简介

这是一个基于Windows生物识别框架(wnbios)的游戏内存数据读取工具，专门用于读取游戏中的玩家信息，如血量、位置、队伍等数据。

## 功能特性

### 核心功能
- ✅ 安全的内存读取（基于wnbios驱动）
- ✅ 玩家数据提取（血量、位置、队伍信息）
- ✅ 实时数据监控
- ✅ 多进程支持
- ✅ 指针验证和错误处理

### 支持的数据类型
- 玩家血量和最大血量
- 玩家3D位置坐标
- 队伍ID和阵营信息
- 玩家存活状态
- 玩家间距离计算

## 文件结构

```
wnbios_poc/
├── drv.h                    # 驱动头文件（已扩展游戏功能）
├── drv.cpp                  # 驱动实现（已添加游戏方法）
├── wnbios_poc.cpp          # 原始测试程序
├── game_reader.cpp         # 完整的游戏数据读取器
├── simple_game_test.cpp    # 简化测试版本
└── README_GAME_READER.md   # 本文档
```

## 编译说明

### 环境要求
- Visual Studio 2019/2022
- Windows 10/11 (x64)
- 管理员权限

### 编译步骤
1. 打开 Visual Studio
2. 加载项目文件 `wnbios_poc.vcxproj`
3. 选择 Release x64 配置
4. 编译项目

### 编译目标
- `wnbios_poc.exe` - 原始测试程序
- `game_reader.exe` - 完整游戏数据读取器
- `simple_game_test.exe` - 简化测试版本

## 使用方法

### 1. 简化测试版本 (推荐新手)

```bash
# 以管理员身份运行
simple_game_test.exe
```

**功能:**
- 自动检测可用进程
- 基本内存读取测试
- PE头验证
- 指针有效性测试

### 2. 完整游戏读取器

```bash
# 以管理员身份运行
game_reader.exe
```

**功能菜单:**
1. 单次扫描玩家数据
2. 实时监控玩家数据
3. 测试游戏进程连接
4. 列出所有进程
5. 退出程序

### 3. 使用示例

#### 单次扫描
```
[*] 请输入目标进程名: DeltaForceClient-Win64-Shipping.exe
[*] 请选择操作: 1

=== 本地玩家信息 ===
地址: 0x7FF6A2B40000
血量: 100.0/100.0 (100.0%)
队伍: 1 | 阵营: 0
位置: (1234.56, 2345.67, 345.78)
状态: 存活

=== 敌方玩家列表 ===
敌方玩家#1 - 血量:85.0/100.0 队伍:2 距离:156.7m
    位置:(1100.23, 2200.45, 340.12) 状态:存活
```

#### 实时监控
```
[*] 请选择操作: 2
[*] 输入扫描间隔(毫秒，默认2000): 1500
[*] 开始实时监控，扫描间隔: 1500ms
[*] 按任意键停止监控...

=== 扫描 #1 [14:30:15] ===
[本地玩家] 血量:95.0/100.0 位置:(1234,2345,345)
[敌方玩家] 2个敌人在范围内
```

## 游戏偏移量配置

当前支持的游戏偏移量在 `drv.h` 中的 `GameOffsets` 命名空间中定义：

```cpp
namespace GameOffsets {
    constexpr DWORD_PTR Uworld = 0x1264C588;
    constexpr DWORD_PTR OwningGameInstance = 0x1B0;
    constexpr DWORD_PTR LocalPlayers = 0x48;
    // ... 更多偏移量
}
```

### 更新偏移量
1. 使用CE (Cheat Engine) 或其他工具找到新的偏移量
2. 修改 `drv.h` 中的相应值
3. 重新编译程序

## 安全注意事项

### 使用前须知
- ⚠️ 本工具仅用于学习和研究目的
- ⚠️ 请确保遵守游戏的使用条款
- ⚠️ 使用时可能被反作弊系统检测
- ⚠️ 建议在测试环境中使用

### 技术安全
- 使用Windows生物识别框架，相对隐蔽
- 自动清理驱动文件和服务
- 包含异常处理和资源管理
- 支持安全的内存读取验证

## 故障排除

### 常见问题

**1. "Failed to write driver file"**
- 解决：以管理员身份运行程序
- 检查防病毒软件是否阻止

**2. "Process not found"**
- 解决：确认进程名拼写正确
- 检查目标进程是否正在运行
- 尝试使用进程列表功能查看可用进程

**3. "UWorld地址无效"**
- 解决：游戏偏移量可能已过期
- 需要更新 `GameOffsets` 中的偏移量
- 检查游戏版本是否匹配

**4. "读取内存失败"**
- 解决：目标进程可能有保护机制
- 尝试重启程序和游戏
- 检查进程权限

### 调试模式
程序包含详细的调试输出，可以帮助诊断问题：

```
[*] 初始化游戏数据读取器...
[成功] 游戏基址: 0x7FF6A2B40000
[验证] 开始验证游戏偏移量...
[成功] UWorld验证通过: 0x1A2B3C4D5E6F
```

## 开发说明

### 添加新功能
1. 在 `drv.h` 中声明新方法
2. 在 `drv.cpp` 中实现方法
3. 在主程序中调用新功能

### 扩展游戏支持
1. 分析目标游戏的内存结构
2. 更新 `GameOffsets` 偏移量
3. 根据需要修改数据结构

## 版本历史

- v1.0 - 初始版本，支持基本玩家数据读取
- 基于原始 wnbios_poc 项目扩展

## 免责声明

本工具仅供学习和研究使用。使用者需要自行承担使用风险，开发者不对任何后果负责。请确保在使用前了解相关法律法规和游戏条款。
