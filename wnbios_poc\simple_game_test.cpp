#define _CRT_SECURE_NO_WARNINGS
#include <Windows.h>
#include <iostream>
#include <signal.h>

#include "drv.h"

// 全局驱动指针
eneio_lib* g_driver = nullptr;

// 信号处理函数
void signal_handler(int signal) {
    printf("\n[*] 程序中断，正在清理...\n");
    if (g_driver) {
        delete g_driver;
        g_driver = nullptr;
    }
    exit(0);
}

int main() {
    printf("=== 简化版游戏数据读取测试 ===\n");
    
    // 设置信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    try {
        printf("[*] 正在初始化wnbios驱动...\n");
        eneio_lib driver;
        g_driver = &driver;
        
        // 测试进程名（可以修改为实际的游戏进程）
        const char* test_processes[] = {
            "notepad.exe",
            "calc.exe", 
            "explorer.exe",
            "DeltaForceClient-Win64-Shipping.exe"
        };
        
        printf("\n[*] 正在搜索测试进程...\n");
        
        // 首先列出所有进程
        driver.list_running_processes();
        
        // 尝试找到一个可用的测试进程
        const char* target_process = nullptr;
        for (const char* process : test_processes) {
            uintptr_t base = driver.get_process_base(process);
            if (base) {
                printf("[找到] %s - 基址: 0x%llX\n", process, base);
                target_process = process;
                break;
            }
        }
        
        if (!target_process) {
            printf("[警告] 未找到任何测试进程\n");
            printf("[*] 请启动以下任一程序进行测试:\n");
            for (const char* process : test_processes) {
                printf("    - %s\n", process);
            }
            printf("\n[*] 按任意键退出...\n");
            system("pause");
            return 0;
        }
        
        printf("\n[*] 使用进程: %s\n", target_process);
        
        // 测试基本内存读取
        printf("\n=== 基本内存读取测试 ===\n");
        uintptr_t process_base = driver.get_process_base(target_process);
        printf("[测试] 进程基址: 0x%llX\n", process_base);
        
        // 读取PE头
        BYTE pe_header[64] = {0};
        if (driver.read_virtual_memory(process_base, pe_header, 64)) {
            printf("[成功] PE头读取成功\n");
            printf("[数据] 前16字节: ");
            for (int i = 0; i < 16; i++) {
                printf("%02X ", pe_header[i]);
            }
            printf("\n");
            
            // 验证MZ签名
            if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) {
                printf("[验证] 有效的PE文件 (MZ签名)\n");
            } else {
                printf("[警告] 无效的PE签名\n");
            }
        } else {
            printf("[错误] PE头读取失败\n");
        }
        
        // 测试模块枚举
        printf("\n=== 模块枚举测试 ===\n");
        driver.enumerate_process_modules(target_process);
        
        // 如果是游戏进程，尝试游戏数据读取
        if (strstr(target_process, "DeltaForce") != nullptr) {
            printf("\n=== 游戏数据读取测试 ===\n");
            
            // 初始化游戏读取器
            if (driver.initialize_game_reader(target_process)) {
                printf("[成功] 游戏读取器初始化完成\n");
                
                // 尝试获取本地玩家
                uintptr_t local_player = driver.get_local_player(target_process);
                if (local_player) {
                    printf("[成功] 本地玩家地址: 0x%llX\n", local_player);
                    
                    // 读取玩家信息
                    GameStructures::PlayerInfo player_info = driver.read_player_info(local_player);
                    if (player_info.isValid) {
                        printf("[玩家信息]\n");
                        printf("  血量: %.1f/%.1f\n", player_info.health, player_info.maxHealth);
                        printf("  队伍: %d\n", player_info.teamId);
                        printf("  位置: (%.2f, %.2f, %.2f)\n", 
                               player_info.position.x, player_info.position.y, player_info.position.z);
                        printf("  状态: %s\n", player_info.isAlive ? "存活" : "死亡");
                    } else {
                        printf("[警告] 玩家信息无效\n");
                    }
                    
                    // 扫描所有玩家
                    printf("\n[*] 扫描所有玩家...\n");
                    std::vector<GameStructures::PlayerInfo> all_players = driver.get_all_players(target_process);
                    printf("[结果] 找到 %zu 个玩家\n", all_players.size());
                    
                } else {
                    printf("[错误] 无法获取本地玩家\n");
                }
            } else {
                printf("[错误] 游戏读取器初始化失败\n");
            }
        } else {
            printf("\n[*] 非游戏进程，跳过游戏数据测试\n");
        }
        
        // 测试指针验证
        printf("\n=== 指针验证测试 ===\n");
        uintptr_t test_addresses[] = {
            0x0,                    // 空指针
            0x1000,                 // 低地址
            process_base,           // 有效地址
            0x7FFFFFFFFFFF,         // 边界地址
            0x8000000000000000      // 无效高地址
        };
        
        for (uintptr_t addr : test_addresses) {
            bool valid = driver.is_valid_pointer(addr);
            printf("[测试] 0x%llX -> %s\n", addr, valid ? "有效" : "无效");
        }
        
        printf("\n=== 测试完成 ===\n");
        printf("[*] 所有基本功能测试完成\n");
        printf("[*] 如需进行实时监控，请使用 game_reader.exe\n");
        printf("\n[*] 按任意键退出...\n");
        system("pause");
        
        g_driver = nullptr;
        return 0;
        
    } catch (const std::exception& e) {
        printf("[-] 异常: %s\n", e.what());
        printf("[*] 按任意键退出...\n");
        system("pause");
        return -1;
    } catch (...) {
        printf("[-] 未知异常\n");
        printf("[*] 按任意键退出...\n");
        system("pause");
        return -1;
    }
}
