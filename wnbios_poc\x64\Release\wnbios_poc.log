﻿  drv.cpp
C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(450,12): error C2374: “link_start”: 重定义；多次初始化
      C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(444,12):
      参见“link_start”的声明
  
C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(450,12): error C2086: “uintptr_t link_start”: 重定义
      C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(444,12):
      参见“link_start”的声明
  
C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(451,12): error C2374: “flink”: 重定义；多次初始化
      C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(445,12):
      参见“flink”的声明
  
C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(451,12): error C2086: “uintptr_t flink”: 重定义
      C:\Users\<USER>\Desktop\驱动漏洞\test2\wnbios_poc-main\wnbios_poc\drv.cpp(445,12):
      参见“flink”的声明
  
  wnbios_poc.cpp
