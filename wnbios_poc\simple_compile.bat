@echo off
echo ========================================
echo      简单编译脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [+] 管理员权限确认
) else (
    echo [-] 需要管理员权限运行此脚本
    echo [*] 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [*] 请确保您已经在 "Developer Command Prompt for VS" 中运行此脚本
echo [*] 或者先运行 vcvars64.bat 设置编译环境
echo.
echo [*] 按任意键继续编译...
pause >nul

REM 清理和创建目录
echo [*] 清理旧文件...
if exist "bin" rmdir /s /q bin
mkdir bin
del *.obj >nul 2>&1
del *.pdb >nul 2>&1
del *.ilk >nul 2>&1

echo [*] 开始编译...
echo.

REM 编译简化测试版本
echo [1/3] 编译简化测试版本...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\simple_game_test.exe simple_game_test.cpp drv.cpp ntdll.lib
if %errorLevel% == 0 (
    echo [+] simple_game_test.exe 编译成功
    set "COMPILE1=OK"
) else (
    echo [-] simple_game_test.exe 编译失败
    set "COMPILE1=FAIL"
)

echo.

REM 编译完整游戏读取器
echo [2/3] 编译完整游戏读取器...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\game_reader.exe game_reader.cpp drv.cpp ntdll.lib
if %errorLevel% == 0 (
    echo [+] game_reader.exe 编译成功
    set "COMPILE2=OK"
) else (
    echo [-] game_reader.exe 编译失败
    set "COMPILE2=FAIL"
)

echo.

REM 编译原始测试程序
echo [3/3] 编译原始测试程序...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\wnbios_poc.exe wnbios_poc.cpp drv.cpp ntdll.lib
if %errorLevel% == 0 (
    echo [+] wnbios_poc.exe 编译成功
    set "COMPILE3=OK"
) else (
    echo [-] wnbios_poc.exe 编译失败
    set "COMPILE3=FAIL"
)

REM 清理临时文件
echo.
echo [*] 清理临时文件...
del *.obj >nul 2>&1
del *.pdb >nul 2>&1
del *.ilk >nul 2>&1

echo.
echo ========================================
echo           编译结果
echo ========================================
echo.

if "%COMPILE1%"=="OK" echo [+] simple_game_test.exe - 编译成功
if "%COMPILE1%"=="FAIL" echo [-] simple_game_test.exe - 编译失败

if "%COMPILE2%"=="OK" echo [+] game_reader.exe - 编译成功
if "%COMPILE2%"=="FAIL" echo [-] game_reader.exe - 编译失败

if "%COMPILE3%"=="OK" echo [+] wnbios_poc.exe - 编译成功
if "%COMPILE3%"=="FAIL" echo [-] wnbios_poc.exe - 编译失败

echo.

if "%COMPILE1%"=="OK" if "%COMPILE2%"=="OK" if "%COMPILE3%"=="OK" (
    echo [成功] 所有程序编译成功！
    echo.
    echo 编译输出:
    dir bin\*.exe
    echo.
    echo 使用说明:
    echo   1. 以管理员身份运行 bin\simple_game_test.exe 进行基本测试
    echo   2. 以管理员身份运行 bin\game_reader.exe 进行完整功能测试
    echo.
) else (
    echo [失败] 部分或全部程序编译失败
    echo.
    echo 可能的解决方案:
    echo   1. 确保在 "Developer Command Prompt for VS" 中运行
    echo   2. 或者先运行: "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo   3. 检查Visual Studio是否正确安装
    echo   4. 确保所有源文件都在当前目录
)

echo.
echo [*] 按任意键退出...
pause >nul
