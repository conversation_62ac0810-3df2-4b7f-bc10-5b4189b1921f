﻿  Assembling Spoofcall.masm...
  Assembling syscall.asm...
  Aimbot.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“Aimbot.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(14,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(15,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(16,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(17,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(18,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(19,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(20,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(21,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(22,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(23,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(24,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(25,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(26,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(27,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(28,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(29,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(30,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(31,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(32,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(33,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(34,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(35,16): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(36,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(37,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(38,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(39,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(82,20): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Aimbot.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Aimbot.cpp(59,9): warning C4244: “return”: 从“double”转换到“float”，可能丢失数据
  Class.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Class.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“Class.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.cpp(636,49): error C2039: "clamp": 不是 "std" 的成员
      F:\Program Files (x86)\Microsoft Visual Studio\2022\VC\Tools\MSVC\14.42.34433\include\algorithm(260,1):
      参见“std”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.cpp(636,49): error C3861: “clamp”: 找不到标识符
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.cpp(636,88): error C2039: "clamp": 不是 "std" 的成员
      F:\Program Files (x86)\Microsoft Visual Studio\2022\VC\Tools\MSVC\14.42.34433\include\algorithm(260,1):
      参见“std”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.cpp(636,88): error C3861: “clamp”: 找不到标识符
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.cpp(636,127): error C2039: "clamp": 不是 "std" 的成员
      F:\Program Files (x86)\Microsoft Visual Studio\2022\VC\Tools\MSVC\14.42.34433\include\algorithm(260,1):
      参见“std”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.cpp(636,127): error C3861: “clamp”: 找不到标识符
  dllmain.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“dllmain.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(14,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(15,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(16,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(17,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(18,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(19,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(20,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(21,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(22,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(23,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(24,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(25,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(26,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(27,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(28,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(29,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(30,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(31,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(32,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(33,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(34,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(35,16): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(36,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(37,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(38,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(39,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(82,20): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“dllmain.cpp”)
  
  Engine.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Engine.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“Engine.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
  hook.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“hook.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(14,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(15,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(16,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(17,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(18,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(19,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(20,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(21,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(22,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(23,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(24,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(25,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(26,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(27,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(28,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(29,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(30,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(31,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(32,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(33,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(34,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(35,16): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(36,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(37,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(38,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(39,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\hooks.h(82,20): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“hook.cpp”)
  
  Hooks.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(14,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(15,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(16,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(17,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(18,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(19,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(20,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(21,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(22,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(23,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(24,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(25,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(26,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(27,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(28,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(29,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(30,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(31,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(32,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(33,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(34,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(35,16): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(36,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(37,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(38,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(39,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(82,20): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(232,70): error C2975: “Cs”:“jm::detail::tstring_”的模板参数无效，应为编译时常量表达式
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      参见“Cs”的声明
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(232,70):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,39):
          查看对正在编译的函数 模板 实例化“auto jm::make_xorstr<AimBotText::RenderText::<lambda_1>,0,1,2,3,4,5,6,7,8,9,10,11,12,0,1,2,3>(Tstr,std::integer_sequence<size_t,0,1,2,3,4,5,6,7,8,9,10,11,12>,std::integer_sequence<size_t,0,1,2,3>) noexcept”的引用
          with
          [
              Tstr=AimBotText::RenderText::<lambda_1>
          ]
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(88,54): error C2131: 表达式的计算结果不是常数
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(88,54):
      遇到非常量(子)表达式
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(88,54):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(233,68):
          查看对正在编译的 类 模板 实例化“jm::xor_string<jm::detail::tstring_<0,0,0,0,0,0,0,0,0,0,0,0,0>,jm::detail::_ki<0,12418927661723533295>,jm::detail::_ki<1,12856220548403275100>,jm::detail::_ki<2,16229657623697189585>,jm::detail::_ki<3,12114952446598806214>>”的引用
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(146,20):
          查看对正在编译的 类 模板 实例化“jm::detail::tstring_<0,0,0,0,0,0,0,0,0,0,0,0,0>”的引用
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(90,69): error C2070: “const unknown-type [13]”: 非法的 sizeof 操作数
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(146,48): warning C4200: 使用了非标准扩展: 结构/联合中的零大小数组
  (编译源文件“Hooks.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(146,48):
      默认构造函数或 copy/move 赋值运算符将忽略此成员
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,4): error C2665: “swprintf”: 没有重载函数可以转换所有参数类型
  (编译源文件“Hooks.cpp”)
      F:\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h(1803,24):
      可能是“int swprintf(wchar_t *const ,const wchar_t *const ,...) throw()”
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,4):
          “int swprintf(wchar_t *const ,const wchar_t *const ,...) throw()”: 无法将参数 2 从“unsigned __int64”转换为“const wchar_t *const ”
              C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,21):
              从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
      F:\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h(1457,37):
      或    “int swprintf(wchar_t *const ,const size_t,const wchar_t *const ,...)”
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,4):
          “int swprintf(wchar_t *const ,const size_t,const wchar_t *const ,...)”: 无法将参数 3 从“unknown-type *”转换为“const wchar_t *const ”
              C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,39):
              指向的类型不相关; 转换需要 reinterpret_cast、C 样式强制转换或带圆括号的函数样式强制转换
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(16,4):
      尝试匹配参数列表“(wchar_t [1024], unsigned __int64, unknown-type *, const _Elem *)”时
          with
          [
              _Elem=char
          ]
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(90,69): error C2070: “const unknown-type [17]”: 非法的 sizeof 操作数
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(20,4): error C2665: “swprintf”: 没有重载函数可以转换所有参数类型
  (编译源文件“Hooks.cpp”)
      F:\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h(1803,24):
      可能是“int swprintf(wchar_t *const ,const wchar_t *const ,...) throw()”
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(20,4):
          “int swprintf(wchar_t *const ,const wchar_t *const ,...) throw()”: 无法将参数 2 从“unsigned __int64”转换为“const wchar_t *const ”
              C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(20,21):
              从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
      F:\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h(1457,37):
      或    “int swprintf(wchar_t *const ,const size_t,const wchar_t *const ,...)”
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(20,4):
          “int swprintf(wchar_t *const ,const size_t,const wchar_t *const ,...)”: 无法将参数 3 从“unknown-type *”转换为“const wchar_t *const ”
              C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(20,39):
              指向的类型不相关; 转换需要 reinterpret_cast、C 样式强制转换或带圆括号的函数样式强制转换
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(20,4):
      尝试匹配参数列表“(wchar_t [1024], unsigned __int64, unknown-type *, const _Elem *)”时
          with
          [
              _Elem=char
          ]
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(90,69): error C2070: “const unknown-type [19]”: 非法的 sizeof 操作数
  (编译源文件“Hooks.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(25,4): error C2665: “swprintf”: 没有重载函数可以转换所有参数类型
  (编译源文件“Hooks.cpp”)
      F:\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h(1803,24):
      可能是“int swprintf(wchar_t *const ,const wchar_t *const ,...) throw()”
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(25,4):
          “int swprintf(wchar_t *const ,const wchar_t *const ,...) throw()”: 无法将参数 2 从“unsigned __int64”转换为“const wchar_t *const ”
              C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(25,21):
              从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
      F:\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h(1457,37):
      或    “int swprintf(wchar_t *const ,const size_t,const wchar_t *const ,...)”
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(25,4):
          “int swprintf(wchar_t *const ,const size_t,const wchar_t *const ,...)”: 无法将参数 3 从“unknown-type *”转换为“const wchar_t *const ”
              C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(25,39):
              指向的类型不相关; 转换需要 reinterpret_cast、C 样式强制转换或带圆括号的函数样式强制转换
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\AimText.h(25,4):
      尝试匹配参数列表“(wchar_t [1024], unsigned __int64, unknown-type *, uint32_t)”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(232,70): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“Hooks.cpp”)
  
  iteam.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“iteam.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(14,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(15,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(16,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(17,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(18,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(19,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(20,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(21,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(22,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(23,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(24,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(25,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(26,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(27,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(28,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(29,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(30,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(31,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(32,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(33,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(34,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(35,16): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(36,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(37,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(38,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(39,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(82,20): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“iteam.cpp”)
  
  Render.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“Render.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\class.h(268,34):
      类型是“UCanvas *”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(14,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(15,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(16,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(17,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(18,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(19,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(20,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(21,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(22,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(23,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(24,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(25,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(26,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(27,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(28,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(29,15): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(30,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(31,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(32,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(33,22): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(34,14): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(35,16): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(36,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(37,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(38,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(39,17): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Hooks.h(82,20): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Render.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Render.cpp(138,46): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Render.cpp(138,34): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Render.cpp(153,47): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Render.cpp(153,35): warning C4244: “参数”: 从“int”转换到“float”，可能丢失数据
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Render.cpp(575,34): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Render.cpp(576,34): warning C4244: “=”: 从“double”转换到“float”，可能丢失数据
  Tools.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Tools.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Tools.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Tools.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Tools.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Tools.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
  Utils.cpp
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,23):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(74,30):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22): error C3533: 参数不能为包含“auto”的类型
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(84,22):
      非类型模板参数中的“auto”至少需要“/std:c++17”
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(161,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_256(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(163,71):
      模板实例化上下文(最早的实例化上下文)为
          C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(145,11):
          在编译类模板“jm::xor_string”时
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(177,71): error C7518: 折叠表达式至少需要 "/std:c++17"
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\xor.h(175,33):
      此诊断出现在编译器生成的函数“void jm::xor_string<T,Keys...>::_crypt_128(const uint64_t *,std::integer_sequence<size_t,Idxs...>) noexcept”中
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(161,8): warning C4099: “FLinearColor”: 类型名称以前使用“struct”现在使用的是“class”
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(130,8):
      参见“FLinearColor”的声明
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C7525: 内联变量至少需要 "/std:c++17"
  (编译源文件“Utils.cpp”)
  
C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,25): error C2864: UCanvas::Canvas: 带有类内初始化表达式的静态 数据成员 必须具有不可变的常量整型类型，或必须被指定为“内联”
  (编译源文件“Utils.cpp”)
      C:\Users\<USER>\Desktop\新建文件夹\cfDMA.tlog\55\BBZ\三角洲\Class.h(268,34):
      类型是“UCanvas *”
  
  正在生成代码...
