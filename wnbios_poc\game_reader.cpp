#define _CRT_SECURE_NO_WARNINGS
#include <Windows.h>
#include <iostream>
#include <vector>
#include <signal.h>
#include <time.h>
#include <conio.h>

#include "drv.h"

// 全局驱动指针用于信号处理
eneio_lib* g_driver = nullptr;

// 信号处理函数
void signal_handler(int signal) {
    printf("\n[*] 接收到中断信号 (%d)，正在清理...\n", signal);
    if (g_driver) {
        g_driver->stop_player_monitoring();
        delete g_driver;
        g_driver = nullptr;
    }
    exit(0);
}

// 安全的字符串输入函数
void safe_input(char* buffer, size_t buffer_size, const char* prompt) {
    printf("%s", prompt);
    if (fgets(buffer, (int)buffer_size, stdin)) {
        // 移除换行符
        size_t len = strlen(buffer);
        if (len > 0 && buffer[len - 1] == '\n') {
            buffer[len - 1] = '\0';
        }
    }
}

// 获取当前时间字符串
void get_timestamp(char* buffer, size_t buffer_size) {
    time_t rawtime;
    struct tm* timeinfo;
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", timeinfo);
}

// 显示菜单
void show_menu() {
    printf("\n==================================================\n");
    printf("           基于wnbios的游戏数据读取器\n");
    printf("==================================================\n");
    printf("1. 单次扫描玩家数据\n");
    printf("2. 实时监控玩家数据\n");
    printf("3. 测试游戏进程连接\n");
    printf("4. 列出所有进程\n");
    printf("5. 退出程序\n");
    printf("==================================================\n");
}

// 单次扫描玩家数据
void single_scan(eneio_lib& driver, const char* process_name) {
    printf("\n=== 单次扫描模式 ===\n");
    
    // 初始化游戏读取器
    if (!driver.initialize_game_reader(process_name)) {
        printf("[错误] 游戏读取器初始化失败\n");
        return;
    }
    
    // 获取本地玩家
    uintptr_t localPlayer = driver.get_local_player(process_name);
    if (!localPlayer) {
        printf("[错误] 无法获取本地玩家\n");
        return;
    }
    
    // 读取本地玩家信息
    GameStructures::PlayerInfo localInfo = driver.read_player_info(localPlayer);
    printf("\n=== 本地玩家信息 ===\n");
    printf("地址: 0x%llX\n", localInfo.address);
    printf("血量: %.1f/%.1f (%.1f%%)\n", 
           localInfo.health, localInfo.maxHealth, 
           (localInfo.health / localInfo.maxHealth) * 100);
    printf("队伍: %d | 阵营: %d\n", localInfo.teamId, localInfo.campId);
    printf("位置: (%.2f, %.2f, %.2f)\n", 
           localInfo.position.x, localInfo.position.y, localInfo.position.z);
    printf("状态: %s\n", localInfo.isAlive ? "存活" : "死亡");
    
    // 获取所有玩家
    std::vector<GameStructures::PlayerInfo> allPlayers = driver.get_all_players(process_name);
    
    printf("\n=== 所有玩家列表 ===\n");
    int enemyCount = 0;
    int teamCount = 0;
    
    for (const auto& player : allPlayers) {
        // 跳过本地玩家
        if (player.address == localPlayer) continue;
        
        // 队伍判断
        bool isEnemy = (player.teamId != localInfo.teamId);
        float distance = localInfo.position.Distance(player.position);
        
        if (isEnemy) {
            enemyCount++;
            printf("敌方玩家#%d - 血量:%.1f/%.1f 队伍:%d 距离:%.1fm\n",
                   enemyCount, player.health, player.maxHealth, player.teamId, distance);
            printf("    位置:(%.1f, %.1f, %.1f) 状态:%s\n",
                   player.position.x, player.position.y, player.position.z,
                   player.isAlive ? "存活" : "死亡");
        } else {
            teamCount++;
            printf("队友#%d - 血量:%.1f/%.1f 距离:%.1fm\n",
                   teamCount, player.health, player.maxHealth, distance);
        }
    }
    
    printf("\n=== 扫描总结 ===\n");
    printf("总玩家数: %zu\n", allPlayers.size());
    printf("敌方玩家: %d\n", enemyCount);
    printf("队友数量: %d\n", teamCount);
}

// 实时监控模式
void real_time_monitoring(eneio_lib& driver, const char* process_name) {
    printf("\n=== 实时监控模式 ===\n");
    
    // 初始化游戏读取器
    if (!driver.initialize_game_reader(process_name)) {
        printf("[错误] 游戏读取器初始化失败\n");
        return;
    }
    
    // 设置扫描间隔
    char interval_input[32] = {0};
    int scan_interval = 2000;
    safe_input(interval_input, sizeof(interval_input), 
               "[*] 输入扫描间隔(毫秒，默认2000): ");
    
    if (strlen(interval_input) > 0) {
        scan_interval = atoi(interval_input);
        if (scan_interval < 500) scan_interval = 500; // 最小500ms
    }
    
    printf("[*] 开始实时监控，扫描间隔: %dms\n", scan_interval);
    printf("[*] 按任意键停止监控...\n");
    
    // 开始监控
    driver.start_player_monitoring(process_name, scan_interval);
    
    // 等待用户按键
    _getch();
    
    // 停止监控
    driver.stop_player_monitoring();
    printf("\n[*] 监控已停止\n");
}

// 测试游戏进程连接
void test_game_connection(eneio_lib& driver, const char* process_name) {
    printf("\n=== 测试游戏进程连接 ===\n");
    
    // 获取进程基址
    uintptr_t base = driver.get_process_base(process_name);
    if (!base) {
        printf("[错误] 找不到进程: %s\n", process_name);
        return;
    }
    
    printf("[成功] 进程基址: 0x%llX\n", base);
    
    // 读取PE头验证
    BYTE pe_header[16] = {0};
    if (driver.read_virtual_memory(base, pe_header, 16)) {
        printf("[成功] PE头读取成功: ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", pe_header[i]);
        }
        printf("\n");
        
        if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) {
            printf("[验证] 有效的PE文件 (MZ签名)\n");
        } else {
            printf("[警告] 无效的PE文件签名\n");
        }
    } else {
        printf("[错误] 无法读取PE头\n");
        return;
    }
    
    // 测试游戏偏移量
    if (driver.validate_game_offsets(process_name)) {
        printf("[成功] 游戏偏移量验证通过\n");
    } else {
        printf("[警告] 游戏偏移量验证失败\n");
    }
    
    printf("[完成] 连接测试完成\n");
}

int main() {
    printf("=== 基于wnbios的游戏数据读取器 ===\n");
    printf("版本: 1.0\n");
    printf("作者: AI Assistant\n");
    printf("描述: 使用Windows生物识别框架进行游戏内存读取\n\n");
    
    // 设置信号处理器
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    try {
        eneio_lib driver;
        g_driver = &driver;
        
        // 首先列出所有运行的进程
        printf("[*] 正在列出所有运行的进程...\n");
        driver.list_running_processes();
        
        // 获取目标进程名
        char process_name[256] = {0};
        printf("\n==================================================\n");
        printf("[*] 常见游戏进程名:\n");
        printf("    - DeltaForceClient-Win64-Shipping.exe (三角洲行动)\n");
        printf("    - notepad.exe (记事本 - 测试用)\n");
        printf("    - calc.exe (计算器 - 测试用)\n");
        printf("    - explorer.exe (资源管理器 - 测试用)\n");
        printf("\n");
        safe_input(process_name, sizeof(process_name), 
                   "[*] 请输入目标进程名: ");
        
        if (strlen(process_name) == 0) {
            printf("[错误] 进程名不能为空\n");
            return -1;
        }
        
        // 主菜单循环
        bool running = true;
        while (running) {
            show_menu();
            
            char choice_input[10] = {0};
            safe_input(choice_input, sizeof(choice_input), "[*] 请选择操作: ");
            int choice = atoi(choice_input);
            
            switch (choice) {
                case 1:
                    single_scan(driver, process_name);
                    break;
                case 2:
                    real_time_monitoring(driver, process_name);
                    break;
                case 3:
                    test_game_connection(driver, process_name);
                    break;
                case 4:
                    printf("\n=== 所有运行进程 ===\n");
                    driver.list_running_processes();
                    break;
                case 5:
                    running = false;
                    break;
                default:
                    printf("[错误] 无效的选择，请重新输入\n");
                    break;
            }
            
            if (running && choice != 4) {
                printf("\n按任意键继续...");
                _getch();
            }
        }
        
        printf("\n[*] 正在退出并清理资源...\n");
        g_driver = nullptr;
        return 0;
        
    } catch (const std::exception& e) {
        printf("[-] 异常发生: %s\n", e.what());
        printf("[*] 按任意键退出...\n");
        _getch();
        return -1;
    } catch (...) {
        printf("[-] 未知异常发生\n");
        printf("[*] 按任意键退出...\n");
        _getch();
        return -1;
    }
}
