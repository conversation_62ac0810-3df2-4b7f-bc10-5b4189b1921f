#define _CRT_SECURE_NO_WARNINGS
#include <Windows.h>
#include <map>
#include <signal.h>
#include <time.h>

#include "drv.h"

// 全局驱动指针用于信号处理
eneio_lib* g_driver = nullptr;

// 信号处理函数
void signal_handler(int signal) {
	printf("\n[*] Received interrupt signal (%d). Cleaning up...\n", signal);
	if (g_driver) {
		delete g_driver;
		g_driver = nullptr;
	}
	exit(0);
}

// 安全的字符串输入函数
void safe_input(char* buffer, size_t buffer_size, const char* prompt) {
	printf("%s", prompt);
	if (fgets(buffer, (int)buffer_size, stdin)) {
		// 移除换行符
		size_t len = strlen(buffer);
		if (len > 0 && buffer[len - 1] == '\n') {
			buffer[len - 1] = '\0';
		}
	}
}

// 获取当前时间字符串
void get_timestamp(char* buffer, size_t buffer_size) {
	time_t rawtime;
	struct tm timeinfo;
	time(&rawtime);

	// 使用安全版本的localtime
	errno_t err = localtime_s(&timeinfo, &rawtime);
	if (err == 0) {
		strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", &timeinfo);
	} else {
		// 如果失败，使用默认时间字符串
		strcpy_s(buffer, buffer_size, "Unknown Time");
	}
}

int main()
{
	printf("[*] Starting eneio64 driver test...\n");
	
	// 设置信号处理器
	signal(SIGINT, signal_handler);
	signal(SIGTERM, signal_handler);
	
	try {
		eneio_lib driver;
		g_driver = &driver;
		
		// 首先列出所有运行的进程
		printf("[*] Listing all running processes...\n");
		driver.list_running_processes();
		
		// 设置扫描次数
		char scan_count_input[32] = { 0 };
		int scan_count = 1;
		printf("\n==================================================\n");
		printf("[*] SCAN CONFIGURATION\n");
		printf("[*] Enter scan count (0 = infinite loop, 1+ = specific count): ");
		safe_input(scan_count_input, sizeof(scan_count_input), "");
		scan_count = atoi(scan_count_input);
		
		if (scan_count == 0) {
			printf("[+] Infinite scan mode enabled (Ctrl+C to stop)\n");
		} else {
			printf("[+] Will perform %d scan(s)\n", scan_count);
		}
		
		// 交互式选择进程
		char process_name[256] = { 0 };
		printf("\n==================================================\n");
		printf("[*] PROCESS ANALYZER - Interactive Mode\n");
		printf("[*] Common processes you can analyze:\n");
		printf("    - notepad.exe (Notepad)\n");
		printf("    - calc.exe (Calculator)\n");
		printf("    - explorer.exe (Windows Explorer)\n");
		printf("    - chrome.exe (Google Chrome)\n");
		printf("    - firefox.exe (Mozilla Firefox)\n");
		printf("    - cmd.exe (Command Prompt)\n");
		printf("\n[*] CONFIRMED AVAILABLE PROCESSES:\n");
		printf("    - notepad.exe (PID 16168) ✓\n");
		printf("    - Taskmgr.exe (Task Manager) ✓\n");
		printf("    - browser.exe ✓\n");
		printf("    - wps.exe ✓\n");
		printf("\n[*] NOTE: DeltaForceClient-Win64-Shipping.exe was NOT found in kernel process list\n");
		printf("    This suggests the process may have anti-debug protection\n");
		printf("\n");
		safe_input(process_name, sizeof(process_name), "[*] Enter the process name to analyze: ");
		
		// 开始扫描循环
		int current_scan = 0;
		int consecutive_failures = 0;
		bool continue_scanning = true;
		
		while (continue_scanning) {
			current_scan++;
			
			// 获取时间戳
			char timestamp[64] = { 0 };
			get_timestamp(timestamp, sizeof(timestamp));
			
			if (scan_count > 0) {
				printf("\n[*] === SCAN %d/%d [%s] ===\n", current_scan, scan_count, timestamp);
			} else {
				printf("\n[*] === SCAN %d (INFINITE MODE) [%s] ===\n", current_scan, timestamp);
			}
			
			printf("[*] Analyzing process: %s\n", process_name);
			
			printf("[*] Searching for process: %s\n", process_name);
			
			// 尝试不同的进程名变体
			uintptr_t base = driver.get_process_base(process_name);
			
			// 如果没找到，尝试截断版本（前15个字符）
			if (!base && strlen(process_name) > 15) {
				char truncated_name[16] = { 0 };
				strncpy_s(truncated_name, sizeof(truncated_name), process_name, 15);
				printf("[*] Trying truncated name: %s\n", truncated_name);
				base = driver.get_process_base(truncated_name);
			}
			
			// 如果还没找到，尝试不带.exe的版本
			if (!base) {
				char* dot_pos = strrchr(process_name, '.');
				if (dot_pos) {
					char name_without_ext[256] = { 0 };
					size_t len = dot_pos - process_name;
					if (len < sizeof(name_without_ext)) {
						strncpy_s(name_without_ext, sizeof(name_without_ext), process_name, len);
						printf("[*] Trying without extension: %s\n", name_without_ext);
						base = driver.get_process_base(name_without_ext);
					}
				}
			}
			
			if (!base)
			{
				printf("[-] Process '%s' is not running or not found\n", process_name);
				printf("[*] This could be due to:\n");
				printf("    1. Process name mismatch (check exact name)\n");
				printf("    2. Process protection/anti-debug\n");
				printf("    3. Insufficient privileges\n");
				
				// 尝试搜索相关进程
				printf("[*] Searching for processes containing 'delta':\n");
				driver.search_processes_by_keyword("delta");
				
				printf("\n[*] Searching for processes containing 'force':\n");
				driver.search_processes_by_keyword("force");
				
				printf("\n[*] Showing all processes for reference:\n");
				driver.list_running_processes();
				
				if (scan_count == 0) {
					printf("[*] Waiting 2 seconds before next scan...\n");
					Sleep(2000);
					continue;
				} else {
					printf("[-] Please check the process name and try again\n");
					system("pause");
					return false;
				}
			}

			printf("[+] %s base address: 0x%llx\n", process_name, base);

			// 读取PE头的前16个字节
			UINT8 buf[16] = { 0 };
			bool success = driver.read_virtual_memory(base, buf, 16);
			
			if (success) {
				printf("[+] PE Header (first 16 bytes): ");
				for (int i = 0; i < 16; i++) {
					printf("%02X ", buf[i]);
				}
				printf("\n");
				
				// 检查是否是有效的PE文件
				if (buf[0] == 0x4D && buf[1] == 0x5A) {
					printf("[+] Valid PE file detected (MZ signature found)\n");
				} else {
					printf("[-] Invalid PE file or read failed\n");
					printf("[-] Expected MZ signature (4D 5A), got: %02X %02X\n", buf[0], buf[1]);
				}
			} else {
				printf("[-] Failed to read memory from address 0x%llx\n", base);
			}
			
			// 枚举进程模块
			printf("\n[*] Enumerating process modules (showing first 10)...\n");
			driver.enumerate_process_modules(process_name);
			
			// 查找特定模块
			printf("\n[*] Searching for common system modules...\n");
			uintptr_t kernel32_base = driver.find_module_base(process_name, L"kernel32.dll");
			if (kernel32_base) {
				printf("[+] kernel32.dll found at: 0x%llx\n", kernel32_base);
			}
			
			uintptr_t ntdll_base = driver.find_module_base(process_name, L"ntdll.dll");
			if (ntdll_base) {
				printf("[+] ntdll.dll found at: 0x%llx\n", ntdll_base);
			}
			
			uintptr_t user32_base = driver.find_module_base(process_name, L"user32.dll");
			if (user32_base) {
				printf("[+] user32.dll found at: 0x%llx\n", user32_base);
			}
			
			// 检查是否继续扫描
			if (scan_count > 0 && current_scan >= scan_count) {
				continue_scanning = false;
			} else if (scan_count == 0) {
				printf("\n[*] Scan %d completed. Waiting 3 seconds before next scan...\n", current_scan);
				printf("[*] Press Ctrl+C to stop infinite scanning\n");
				Sleep(3000);
			}
		}
		
		printf("\n[*] All scans completed (%d total scans)\n", current_scan);
		
		// 询问是否要分析其他进程
		printf("\n==================================================\n");
		printf("[*] Analysis completed for '%s'\n", process_name);
		
		char choice_input[10] = { 0 };
		safe_input(choice_input, sizeof(choice_input), "[*] Do you want to analyze another process? (y/n): ");
		char choice = choice_input[0];
		
		if (choice == 'y' || choice == 'Y') {
			printf("\n============================================================\n");
			printf("[*] Starting new analysis session...\n");
			
			// 重新列出进程
			driver.list_running_processes();
			
			// 重新设置扫描次数
			safe_input(scan_count_input, sizeof(scan_count_input), "\n[*] Enter scan count (0 = infinite, 1+ = specific): ");
			scan_count = atoi(scan_count_input);
			
			// 再次输入进程名
			safe_input(process_name, sizeof(process_name), "[*] Enter the process name to analyze: ");
			
			// 重新开始扫描循环
			current_scan = 0;
			continue_scanning = true;
			
			while (continue_scanning) {
				current_scan++;
				
				if (scan_count > 0) {
					printf("\n[*] === SCAN %d/%d ===\n", current_scan, scan_count);
				} else {
					printf("\n[*] === SCAN %d (INFINITE MODE) ===\n", current_scan);
				}
				
				uintptr_t base2 = driver.get_process_base(process_name);
				if (base2) {
					printf("[+] %s base address: 0x%llx\n", process_name, base2);
					driver.enumerate_process_modules(process_name);
				} else {
					printf("[-] Process '%s' not found\n", process_name);
					if (scan_count == 0) {
						printf("[*] Waiting 2 seconds before next scan...\n");
						Sleep(2000);
						continue;
					} else {
						break;
					}
				}
				
				// 检查是否继续扫描
				if (scan_count > 0 && current_scan >= scan_count) {
					continue_scanning = false;
				} else if (scan_count == 0) {
					printf("\n[*] Scan %d completed. Waiting 3 seconds before next scan...\n", current_scan);
					Sleep(3000);
				}
			}
		}
		
		printf("\n==================================================\n");
		printf("[*] All analysis completed. Press any key to exit and cleanup...\n");
		system("pause");
		
		printf("\n[*] Exiting and cleaning up resources...\n");
		g_driver = nullptr;  // 清理全局指针
		// 析构函数会自动调用清理代码
		return true;
	}
	catch (const std::exception& e) {
		printf("[-] Exception occurred: %s\n", e.what());
		printf("[*] Press any key to exit...\n");
		system("pause");
		return false;
	}
	catch (...) {
		printf("[-] Unknown exception occurred during driver initialization\n");
		printf("[*] Press any key to exit...\n");
		system("pause");
		return false;
	}
}