@echo off
echo ========================================
echo    游戏数据读取器编译脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [+] 管理员权限确认
) else (
    echo [-] 需要管理员权限运行此脚本
    echo [*] 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 设置编译环境
set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build"
if not exist "%VS_PATH%\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build"
)

if not exist "%VS_PATH%\vcvars64.bat" (
    echo [-] 找不到Visual Studio编译环境
    echo [*] 请确保已安装Visual Studio 2019或2022
    pause
    exit /b 1
)

echo [*] 设置编译环境...
call "%VS_PATH%\vcvars64.bat" >nul 2>&1

REM 创建输出目录
if not exist "bin" mkdir bin

echo [*] 开始编译...
echo.

REM 编译简化测试版本
echo [1/3] 编译简化测试版本...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\simple_game_test.exe simple_game_test.cpp drv.cpp ntdll.lib >compile_log.txt 2>&1
if %errorLevel% == 0 (
    echo [+] simple_game_test.exe 编译成功
) else (
    echo [-] simple_game_test.exe 编译失败
    echo [*] 错误详情请查看 compile_log.txt
)

REM 编译完整游戏读取器
echo [2/3] 编译完整游戏读取器...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\game_reader.exe game_reader.cpp drv.cpp ntdll.lib >compile_log.txt 2>&1
if %errorLevel% == 0 (
    echo [+] game_reader.exe 编译成功
) else (
    echo [-] game_reader.exe 编译失败
    echo [*] 错误详情请查看 compile_log.txt
)

REM 编译原始测试程序
echo [3/3] 编译原始测试程序...
cl /EHsc /O2 /MT /D_CRT_SECURE_NO_WARNINGS /wd4996 /Fe:bin\wnbios_poc.exe wnbios_poc.cpp drv.cpp ntdll.lib >compile_log.txt 2>&1
if %errorLevel% == 0 (
    echo [+] wnbios_poc.exe 编译成功
) else (
    echo [-] wnbios_poc.exe 编译失败
    echo [*] 错误详情请查看 compile_log.txt
)

REM 清理临时文件
echo.
echo [*] 清理临时文件...
del *.obj >nul 2>&1

echo.
echo ========================================
echo           编译完成
echo ========================================
echo.
echo 编译输出位置: bin\
echo.
echo 可执行文件:
if exist "bin\simple_game_test.exe" echo   [+] simple_game_test.exe - 简化测试版本
if exist "bin\game_reader.exe" echo   [+] game_reader.exe - 完整游戏读取器  
if exist "bin\wnbios_poc.exe" echo   [+] wnbios_poc.exe - 原始测试程序
echo.
echo 使用说明:
echo   1. 以管理员身份运行可执行文件
echo   2. 推荐新手先使用 simple_game_test.exe
echo   3. 详细说明请查看 README_GAME_READER.md
echo.
echo [*] 按任意键退出...
pause >nul
